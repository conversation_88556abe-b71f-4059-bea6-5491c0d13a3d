import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import './ExportButtons.css';

interface ExportButtonsProps {
  cron: string;
}

const ExportButtons: React.FC<ExportButtonsProps> = ({ cron }) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(cron).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <div className="export-buttons">
      <button onClick={handleCopy} className="copy-button">
        {copied ? t('export.copied') : t('export.copy')}
      </button>
    </div>
  );
};

export default ExportButtons;