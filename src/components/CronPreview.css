.cron-preview {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
}

.preview-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.cron-input-field {
  width: 100%;
  padding: 12px;
  margin-bottom: 15px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-sizing: border-box;
}

.description-box {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #eee;
}

.description-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.description-text {
  margin: 0;
  font-size: 15px;
  color: #555;
  min-height: 22px; /* Prevents layout shift */
}

.control-label {
    margin-bottom: 15px;
    font-weight: bold;
    color: #2c3e50;
}

.cron-string-display {
  background-color: #34495e;
  color: #ffffff;
  padding: 15px;
  border-radius: 5px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 1.2rem;
  font-weight: bold;
  letter-spacing: 1px;
  margin-bottom: 15px;
  word-break: break-all;
}

.human-readable {
  font-style: italic;
  color: #7f8c8d;
  font-size: 1rem;
}
