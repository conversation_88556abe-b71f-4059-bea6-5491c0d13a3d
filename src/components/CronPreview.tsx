import React from 'react';
import { useTranslation } from 'react-i18next';
import cronstrue from 'cronstrue/i18n';
import 'cronstrue/locales/zh_CN';
import './CronPreview.css';

interface CronPreviewProps {
  cron: string;
  onCronInputChange: (newCron: string) => void;
}

const CronPreview: React.FC<CronPreviewProps> = ({ cron, onCronInputChange }) => {
  const { t, i18n } = useTranslation();

  const getCronDescription = () => {
    if (!cron) return '';
    try {
      return cronstrue.toString(cron, {
        locale: i18n.language === 'zh' ? 'zh_CN' : 'en',
        use24HourTimeFormat: true,
        verbose: true,
      });
    } catch (e) {
      return t('preview.invalid_cron');
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onCronInputChange(event.target.value);
  };

  return (
    <div className="cron-preview">
      <h3 className="preview-title">{t('preview.title')}</h3>
      <input
        type="text"
        value={cron}
        onChange={handleInputChange}
        className="cron-input-field"
        placeholder={t('preview.placeholder')}
      />
      <div className="description-box">
        <h4 className="description-title">{t('preview.description')}</h4>
        <p className="description-text">{getCronDescription()}</p>
      </div>
    </div>
  );
};

export default CronPreview;