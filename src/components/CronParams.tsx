import React from 'react';
import { useTranslation } from 'react-i18next';
import { CronData, Period } from '../utils/cronUtils';
import './CronParams.css';

interface CronParamsProps {
  period: Period;
  data: CronData;
  onChange: (data: CronData) => void;
}

const CronParams: React.FC<CronParamsProps> = ({ period, data, onChange }) => {
  const { t } = useTranslation();

  const handleChange = (field: keyof CronData, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <div className="cron-params">
      {period === 'secondly' && <div>{t('params.every_second')}</div>}

      <div className="time-inputs-row">
        {['hourly', 'daily', 'weekly', 'monthly'].includes(period) && (
          <div className="param-row">
            <label>{t('params.second')}</label>
            <input
              type="number"
              min="0"
              max="59"
              value={data.second ?? '0'}
              onChange={e => handleChange('second', e.target.value)}
            />
          </div>
        )}

        {['hourly', 'daily', 'weekly', 'monthly'].includes(period) && (
          <div className="param-row">
            <label>{t('params.minute')}</label>
            <input
              type="number"
              min="0"
              max="59"
              value={data.minute}
              onChange={e => handleChange('minute', e.target.value)}
            />
          </div>
        )}

        {['daily', 'weekly', 'monthly'].includes(period) && (
          <div className="param-row">
            <label>{t('params.hour')}</label>
            <input
              type="number"
              min="0"
              max="23"
              value={data.hour}
              onChange={e => handleChange('hour', e.target.value)}
            />
          </div>
        )}
      </div>

      {period === 'monthly' && (
        <div className="param-row">
          <label>{t('params.day_of_month')}</label>
          <input
            type="number"
            min="1"
            max="31"
            value={data.dayOfMonth}
            onChange={e => handleChange('dayOfMonth', e.target.value)}
          />
        </div>
      )}

      {period === 'weekly' && (
        <div className="param-row">
          <label>{t('params.day_of_week')}</label>
          <div className="day-of-week-selector">
            {['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'].map((day, index) => (
              <button
                key={day}
                className={data.dayOfWeek === index.toString() ? 'selected' : ''}
                onClick={() => handleChange('dayOfWeek', index.toString())}
              >
                {t(`params.days_short.${day.toLowerCase()}`)}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CronParams;