.cron-params {
  background-color: #ecf0f1;
  padding: 20px;
  border-radius: 5px;
  min-height: 150px; /* Ensure a minimum height */
  display: flex;
  flex-direction: column;
}

.control-label {
    margin-bottom: 15px;
    font-weight: bold;
    color: #2c3e50;
}

.params-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.time-controls {
    display: flex;
    gap: 20px;
    align-items: center;
}

.time-inputs-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.time-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.time-input-group label {
    font-size: 0.9rem;
    color: #555;
}

.time-input {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 60px;
    text-align: center;
}

.param-row {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.param-row label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.param-row input[type="number"] {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 16px;
  width: 100%;
  box-sizing: border-box;
}

.day-of-week-selector {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.day-of-week-selector button {
  flex: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.day-of-week-selector button.selected {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.day-button {
    padding: 8px 12px;
    border: 1px solid #bdc3c7;
    background-color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.day-button.active {
    background-color: #3498db;
    color: #fff;
    border-color: #3498db;
}
