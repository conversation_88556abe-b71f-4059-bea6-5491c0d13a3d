import React from 'react';
import { useTranslation } from 'react-i18next';
import { Period } from '../utils/cronUtils';
import './PeriodSelector.css';

interface PeriodSelectorProps {
  selected: Period;
  onChange: (period: Period) => void;
}

const PeriodSelector: React.FC<PeriodSelectorProps> = ({ selected, onChange }) => {
  const { t } = useTranslation();
  const periods: Period[] = [
    'secondly',
    'minutely',
    'hourly',
    'daily',
    'weekly',
    'monthly',
  ];

  return (
    <div className="period-selector">
      <div className="control-label">{t('period.title')}</div>
      <div className="period-buttons">
        {periods.map(p => (
          <button
            key={p}
            className={`period-button ${selected === p ? 'active' : ''}`}
            onClick={() => onChange(p)}
          >
            {t(p)}
          </button>
        ))}
      </div>
    </div>
  );
};

export default PeriodSelector;