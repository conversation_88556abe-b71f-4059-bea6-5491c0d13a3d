import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { parseCronExpression } from 'cron-schedule';
import './NextRunTimes.css';

interface NextRunTimesProps {
  cron: string;
}

const NextRunTimes: React.FC<NextRunTimesProps> = ({ cron }) => {
  const { t } = useTranslation();
  const [nextTimes, setNextTimes] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!cron) {
      setNextTimes([]);
      setError(null);
      return;
    }
    try {
      const task = parseCronExpression(cron);
      const times = task.getNextDates(10);
      setNextTimes(times.map((date: Date) => date.toLocaleString()));
      setError(null);
    } catch (err) {
      setError(t('preview.invalid_cron'));
      setNextTimes([]);
    }
  }, [cron, t]);

  return (
    <div className="next-run-times">
      <h4>{t('preview.next_runs_title')}</h4>
      {error ? (
        <p className="error-message">{error}</p>
      ) : (
        <ul>
          {nextTimes.map((time, index) => (
            <li key={index}>{time}</li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default NextRunTimes;
