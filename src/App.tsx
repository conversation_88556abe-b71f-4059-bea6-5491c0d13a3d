import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import PeriodSelector from './components/PeriodSelector';
import CronParams from './components/CronParams';
import CronPreview from './components/CronPreview';
import ExportButtons from './components/ExportButtons';
import NextRunTimes from './components/NextRunTimes';
import { CronData, generateCronExpression, parseCronExpressionToData, Period } from './utils/cronUtils';
import './App.css';

const initialCronData: CronData = {
  second: '0',
  minute: '0',
  hour: '0',
  dayOfMonth: '*',
  month: '*',
  dayOfWeek: '*',
};

function App() {
  const { t, i18n } = useTranslation();
  const [period, setPeriod] = useState<Period>('daily');
  const [cronData, setCronData] = useState<CronData>(initialCronData);
  const [cron, setCron] = useState(generateCronExpression(initialCronData));

  useEffect(() => {
    const newCron = generateCronExpression(cronData);
    setCron(newCron);
  }, [cronData]);

  useEffect(() => {
    const newCronData: CronData = {
      second: '*',
      minute: '*',
      hour: '*',
      dayOfMonth: '*',
      month: '*',
      dayOfWeek: '*',
    };

    switch (period) {
      case 'secondly':
        break;
      case 'minutely':
        newCronData.second = '0';
        break;
      case 'hourly':
        newCronData.second = '0';
        newCronData.minute = '0';
        break;
      case 'daily':
        newCronData.second = '0';
        newCronData.minute = '0';
        newCronData.hour = '0';
        break;
      case 'weekly':
        newCronData.second = '0';
        newCronData.minute = '0';
        newCronData.hour = '0';
        newCronData.dayOfWeek = '0';
        break;
      case 'monthly':
        newCronData.second = '0';
        newCronData.minute = '0';
        newCronData.hour = '0';
        newCronData.dayOfMonth = '1';
        newCronData.dayOfWeek = '*'; // Ensure dayOfWeek is reset for monthly
        break;
    }
    setCronData(newCronData);
  }, [period]);

  const handleCronInputChange = (newCron: string) => {
    setCron(newCron); // Update the input field immediately
    const parsed = parseCronExpressionToData(newCron);
    if (parsed) {
      setPeriod(parsed.period);
      setCronData(parsed.data);
    }
  };

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="app-container">
      <header className="app-header">
        <div className="language-switcher">
          <select className="language-select" onChange={(e) => changeLanguage(e.target.value)} value={i18n.language.split('-')[0]}>
            <option value="en">English</option>
            <option value="zh">中文</option>
          </select>
        </div>
        <h1>{t('appTitle')}</h1>
        <p>{t('appDescription')}</p>
      </header>
      <main className="cron-generator">
        <PeriodSelector selected={period} onChange={setPeriod} />
        <CronParams period={period} data={cronData} onChange={setCronData} />
        <div className="right-panel">
          <CronPreview cron={cron} onCronInputChange={handleCronInputChange} />
          <ExportButtons cron={cron} />
          <NextRunTimes cron={cron} />
        </div>
      </main>
    </div>
  );
}

export default App;
