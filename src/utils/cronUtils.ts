export type Period =
  | 'secondly'
  | 'minutely'
  | 'hourly'
  | 'daily'
  | 'weekly'
  | 'monthly';

export type CronData = {
  second: string;
  minute: string;
  hour: string;
  dayOfMonth: string;
  month: string;
  dayOfWeek: string;
};

export const generateCronExpression = (data: CronData): string => {
  return `${data.second} ${data.minute} ${data.hour} ${data.dayOfMonth} ${data.month} ${data.dayOfWeek}`;
};

export const parseCronExpressionToData = (
  cron: string,
): { period: Period; data: CronData } | null => {
  const parts = cron.trim().split(' ');
  if (parts.length !== 6) {
    return null;
  }

  // The UI currently only supports simple values, not ranges, lists, or steps.
  const isComplex = (val: string) =>
    val.includes(',') || val.includes('-') || val.includes('/');
  if (parts.some(isComplex)) {
    return null;
  }

  const [second, minute, hour, dayOfMonth, month, dayOfWeek] = parts;
  const data: CronData = { second, minute, hour, dayOfMonth, month, dayOfWeek };

  const isNum = (v: string) => /^\d+$/.test(v);
  const isWild = (v: string) => v === '*';

  // Determine period by matching from most specific to least specific patterns
  if (isNum(dayOfMonth) && isWild(dayOfWeek) && isWild(month)) {
    if (isNum(hour) && isNum(minute) && isNum(second)) {
      return { period: 'monthly', data };
    }
  }

  if (isWild(dayOfMonth) && isNum(dayOfWeek) && isWild(month)) {
    if (isNum(hour) && isNum(minute) && isNum(second)) {
      return { period: 'weekly', data };
    }
  }

  if (isWild(dayOfMonth) && isWild(dayOfWeek) && isWild(month)) {
    if (isNum(hour) && isNum(minute) && isNum(second)) {
      return { period: 'daily', data };
    }
    if (isWild(hour) && isNum(minute) && isNum(second)) {
      return { period: 'hourly', data };
    }
    if (isWild(hour) && isWild(minute) && isNum(second)) {
      return { period: 'minutely', data };
    }
    if (isWild(hour) && isWild(minute) && isWild(second)) {
      return { period: 'secondly', data };
    }
  }

  return null; // Return null if no pattern matches the UI capabilities
};