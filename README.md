# Cron Expression Generator

A simple, single-page React application to generate cron expressions with ease.

## ✨ Features

- **Intuitive UI**: Easily select schedules (minute, hour, day, week, month).
- **Real-time Preview**: Instantly see the generated cron expression and its human-readable translation.
- **Copy & Export**: One-click copy to clipboard and export options.
- **Zero Knowledge Required**: Designed for developers and non-developers alike.

## 🚀 Getting Started

1.  **Install dependencies:**
    ```bash
    npm install
    ```

2.  **Run the development server:**
    ```bash
    npm start
    ```

The app will be available at `http://localhost:3000`.

## 📂 Source Code Structure

The project is structured as a standard `create-react-app` application:

-   `public/`: Contains the main `index.html` file and other static assets like logos and manifests.
-   `src/`: Contains the application's source code.
    -   `index.tsx`: The entry point that renders the `App` component into the DOM.
    -   `App.tsx`: The main application component that holds the state and orchestrates the different parts of the UI.
    -   `i18n.ts`: Configuration file for the `i18next` internationalization library.
    -   `components/`: Contains all the React components.
        -   `PeriodSelector.tsx`: Allows users to select the cron frequency (e.g., daily, weekly).
        -   `CronParams.tsx`: Displays the input fields corresponding to the selected period.
        -   `CronPreview.tsx`: Shows the generated cron expression in real-time.
        -   `NextRunTimes.tsx`: Displays the next scheduled run times.
        -   `ExportButtons.tsx`: Provides buttons to copy or export the cron expression.
    -   `utils/`: Contains utility functions.
        -   `cronUtils.ts`: Core logic for generating and parsing cron expressions.

---

# Cron 表达式生成器 (中文版)

一个简单、单页的 React 应用，可轻松生成 cron 表达式。

## ✨ 功能

-   **直观的用户界面**: 轻松选择计划任务周期（分钟、小时、天、周、月）。
-   **实时预览**: 即时查看生成的 cron 表达式及其可读的文字描述。
-   **复制与导出**: 一键复制到剪贴板和导出选项。
-   **无需专业知识**: 为开发者和非开发者设计。

## 🚀 快速开始

1.  **安装依赖:**
    ```bash
    npm install
    ```

2.  **运行开发服务器:**
    ```bash
    npm start
    ```

    应用将在 `http://localhost:3000` 上可用。

## 📂 源码结构

本项目采用标准的 `create-react-app` 结构：

-   `public/`: 包含主 `index.html` 文件以及其他静态资源，如 logo 和 manifest 文件。
-   `src/`: 包含应用源码。
    -   `index.tsx`: 应用入口文件，将 `App` 组件渲染到 DOM 中。
    -   `App.tsx`: 主应用组件，负责管理状态和协调 UI 各部分。
    -   `i18n.ts`: `i18next` 国际化库的配置文件。
    -   `components/`: 包含所有的 React 组件。
        -   `PeriodSelector.tsx`: 允许用户选择 cron 频率（例如，每天、每周）。
        -   `CronParams.tsx`: 显示与所选周期相对应的输入字段。
        -   `CronPreview.tsx`: 实时显示生成的 cron 表达式。
        -   `NextRunTimes.tsx`: 显示下一次计划的运行时间。
        -   `ExportButtons.tsx`: 提供复制或导出 cron 表达式的按钮。
    -   `utils/`: 包含工具函数。
        -   `cronUtils.ts`: 用于生成和解析 cron 表达式的核心逻辑。
