<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="https://cron.aolifu.org/logo.svg" type="image/svg+xml" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="An intuitive and powerful cron expression generator. Create, edit, and visualize cron schedules with ease. Supports standard and extended cron formats, with live previews and upcoming run time displays."
    />
    <meta name="keywords" content="cron, cron expression, cron generator, cron schedule, cron job, scheduler, task scheduling" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://cron.aolifu.org/" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://cron.aolifu.org/" />
    <meta property="og:title" content="Cron Expression Generator" />
    <meta property="og:description" content="An intuitive and powerful cron expression generator. Create, edit, and visualize cron schedules with ease." />
    <meta property="og:image" content="https://cron.aolifu.org/logo.svg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary" />
    <meta property="twitter:url" content="https://cron.aolifu.org/" />
    <meta property="twitter:title" content="Cron Expression Generator" />
    <meta property="twitter:description" content="An intuitive and powerful cron expression generator. Create, edit, and visualize cron schedules with ease." />
    <meta property="twitter:image" content="https://cron.aolifu.org/logo.svg" />

    <link rel="apple-touch-icon" href="https://cron.aolifu.org/logo.svg" />
    <link rel="manifest" href="https://cron.aolifu.org/manifest.json" />

    <title>Cron Expression Generator</title>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3811349067654166"
            crossorigin="anonymous"></script>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Q7NP31PP2C"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-Q7NP31PP2C');
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

  </body>
</html>
